import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IConversation, IConversationList, ICreateConversation, IMessage, IMessagePage, ISendMessage, IServiceResponse } from '@schema';
import { Observable } from 'rxjs';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  private readonly baseUrl = `${this.UrlService.API_URL}/chat`;

  constructor(private http: HttpClient, private UrlService: UrlService) { }

  // Create or get existing conversation
  createOrGetConversation(targetUserId: number, name?: string): Observable<IServiceResponse<IConversation>> {
    const payload: ICreateConversation = {
      name,
      agentId: targetUserId, // For now, using targetUserId as agentId
      userIdB: targetUserId
    };
    return this.http.post<IServiceResponse<IConversation>>(`${this.baseUrl}/conversation`, payload);
  }

  // Get user's conversations
  getMyConversations(): Observable<IServiceResponse<IConversationList[]>> {
    return this.http.get<IServiceResponse<IConversationList[]>>(`${this.baseUrl}/conversations`);
  }

  // Get specific conversation
  getConversation(conversationId: string): Observable<IServiceResponse<IConversation>> {
    return this.http.get<IServiceResponse<IConversation>>(`${this.baseUrl}/conversation/${conversationId}`);
  }

  // Send message
  sendMessage(request: ISendMessage): Observable<IServiceResponse<IMessage>> {
    return this.http.post<IServiceResponse<IMessage>>(`${this.baseUrl}/message`, request);
  }

  // Get messages from conversation
  getMessages(conversationId: string, page: number = 1, pageSize: number = 50): Observable<IServiceResponse<IMessagePage>> {
    return this.http.get<IServiceResponse<IMessagePage>>(`${this.baseUrl}/messages/${conversationId}?page=${page}&pageSize=${pageSize}`);
  }

  // Get world chat
  getWorldChat(): Observable<IServiceResponse<IConversation>> {
    return this.http.get<IServiceResponse<IConversation>>(`${this.baseUrl}/world`);
  }

  // Send world chat message
  sendWorldChatMessage(content: string): Observable<IServiceResponse<IMessage>> {
    return this.http.post<IServiceResponse<IMessage>>(`${this.baseUrl}/world/message`, JSON.stringify(content), {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
