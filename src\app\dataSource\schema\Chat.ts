export interface IConversation {
  id: string;
  name?: string;
  userIdA: number;
  agentId: number;
  userIdB: number;
  createdAt: string;
  updatedAt: string;
  lastMessageId?: string;
  userA?: IChatUser;
  userB?: IChatUser;
  agent?: IChatUser;
  messages: IMessage[];
  lastMessage?: IMessage;
}

export interface IMessage {
  id: string;
  userId: number;
  content: string;
  roomId: string;
  createdAt: string;
  user?: IChatUser;
}

export interface IConversationList {
  id: string;
  name?: string;
  otherUser?: IChatUser;
  agent?: IChatUser;
  lastMessage?: IMessage;
  updatedAt: string;
  isWorldChat: boolean;
}

export interface ICreateConversation {
  name?: string;
  agentId: number;
  userIdB: number;
}

export interface ISendMessage {
  content: string;
  conversationId?: string;
  targetUserId?: number;
  isWorldChat?: boolean;
}

export interface IMessagePage {
  messages: IMessage[];
  totalCount: number;
  page: number;
  pageSize: number;
  hasNextPage: boolean;
}

export interface IChatUser {
  id: number;
  email?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  dob?: string;
  createAt?: string;
  gender: number;
  token?: string;
  experience: number;
  status?: number;
  maxim?: string;
  typeLevel: number;
}
