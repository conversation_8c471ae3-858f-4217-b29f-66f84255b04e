using ComicApp.Data;
using ComicApp.Models;
using ComicApp.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace ComicApp.Controllers
{
    [ApiController]
    [Route("api/")]
    public class LeaderboardController : ControllerBase
    {
        private readonly ILogger<LeaderboardController> _logger;
        private readonly IUserService _userService;

        public LeaderboardController(ILogger<LeaderboardController> logger, IUserService userService)
        {
            _logger = logger;
            _userService = userService;
        }

        /// <summary>
        /// Get top users by experience points
        /// </summary>
        /// <param name="limit">Number of top users to return (default: 5, max: 20)</param>
        /// <returns>List of top users with their experience points</returns>
        [HttpGet("top-users")]
        public async Task<IActionResult> GetTopUsersByExperience([FromQuery] int limit = 5)
        {
            try
            {
                limit = Math.Clamp(5, limit, 20);
                var data = await _userService.GetTopUsersByExperience(limit);
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving top users by experience");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Internal server error",
                    error = ex.Message
                });
            }
        }

    }
}
