import { Router } from 'express';
import { SyncTrackingController } from './sync-tracking.controller';

const router = Router();
const controller = new SyncTrackingController();
// Test credentials endpoint
router.post('/test-credentials', controller.testCredentials.bind(controller));

// Start sync process
router.post('/start', controller.startSync.bind(controller));

// Get sync settings
router.get('/settings', controller.getSettings.bind(controller));

// Save sync settings
router.post('/settings', controller.saveSettings.bind(controller));

// Get sync history
// router.get('/history', controller.getSyncHistory.bind(controller));

// Get sync progress (for real-time updates)
router.get('/progress/:sessionId', controller.getSyncProgress.bind(controller));

export { router as syncTrackingRouter };

