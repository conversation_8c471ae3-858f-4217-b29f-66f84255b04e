import { CrawlerService, TrackedComic } from './crawler.service.js';

export interface SyncCredentials {
  username: string;
  password: string;
  sourceFrom: 'nettruyen' | 'truyenqq';
  sourceTo: 'local'; // Always sync to local database
}

export interface SyncSettings {
  autoResolveConflicts: boolean;
  preferredSource: 'nettruyen' | 'truyenqq' | 'latest';
  syncInterval: number;
  enableNotifications: boolean;
  backupBeforeSync: boolean;
}

export interface SyncProgress {
  sessionId: string;
  stage: 'idle' | 'connecting' | 'fetching' | 'comparing' | 'syncing' | 'completed' | 'error';
  progress: number;
  message: string;
  startTime: Date;
  endTime?: Date;
  totalComics?: number;
  processedComics?: number;
  errors?: string[];
}

export interface SyncResult {
  sessionId: string;
  success: boolean;
  totalComics: number;
  syncedComics: number;
  skippedComics: number;
  errorComics: number;
  errors: Array<{
    comicId: string;
    comicTitle: string;
    error: string;
    source: string;
  }>;
  summary: {
    sourceComics: number;
    newComics: number;
    updatedComics: number;
    duplicateComics: number;
  };
  duration: number; // in seconds
}

export class SyncTrackingService {
  private crawlerService = new CrawlerService();
  private activeSessions = new Map<string, SyncProgress>();
  private syncHistory: SyncResult[] = [];

  /**
   * Start sync process
   */
  private ramdomUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  async startSync(credentials: SyncCredentials, settings: SyncSettings): Promise<string> {
    const sessionId = this.ramdomUUID();
    const progress: SyncProgress = {
      sessionId,
      stage: 'connecting',
      progress: 0,
      message: 'Initializing sync process...',
      startTime: new Date(),
      errors: []
    };

    this.activeSessions.set(sessionId, progress);

    // Start sync process in background
    this.performSync(sessionId, credentials, settings).catch(error => {
      console.error('Sync process error:', error);
      this.updateProgress(sessionId, {
        stage: 'error',
        progress: 0,
        message: `Sync failed: ${error.message}`
      });
    });

    return sessionId;
  }

  /**
   * Perform the actual sync process
   */
  private async performSync(sessionId: string, credentials: SyncCredentials, settings: SyncSettings): Promise<void> {
    const startTime = Date.now();
    let sourceComics: TrackedComic[] = [];
    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const errors: Array<{ comicId: string; comicTitle: string; error: string; source: string }> = [];

    try {
      // Stage 1: Connecting and testing credentials
      this.updateProgress(sessionId, {
        stage: 'connecting',
        progress: 10,
        message: `Testing connection to ${credentials.sourceFrom}...`
      });

      const isValidCredentials = await this.crawlerService.testLogin(credentials.sourceFrom, credentials);
      if (!isValidCredentials) {
        throw new Error(`Invalid credentials for ${credentials.sourceFrom}`);
      }

      // Stage 2: Fetching comics from source
      this.updateProgress(sessionId, {
        stage: 'fetching',
        progress: 30,
        message: `Fetching comics from ${credentials.sourceFrom}...`
      });

      if (credentials.sourceFrom === 'nettruyen') {
        sourceComics = await this.crawlerService.fetchNettruyenComics(credentials);
      } else {
        sourceComics = await this.crawlerService.fetchTruyenqqComics(credentials);
      }

      this.updateProgress(sessionId, {
        stage: 'fetching',
        progress: 50,
        message: `Found ${sourceComics.length} comics to sync`,
        totalComics: sourceComics.length
      });

      // Stage 3: Comparing and preparing sync
      this.updateProgress(sessionId, {
        stage: 'comparing',
        progress: 60,
        message: 'Analyzing comics for sync...'
      });

      // Stage 4: Syncing comics to local database
      this.updateProgress(sessionId, {
        stage: 'syncing',
        progress: 70,
        message: 'Syncing comics to local database...'
      });

      for (let i = 0; i < sourceComics.length; i++) {
        const comic = sourceComics[i];
        
        try {
          const syncSuccess = await this.crawlerService.syncComicToLocal(comic);
          
          if (syncSuccess) {
            syncedCount++;
          } else {
            skippedCount++;
          }

          // Update progress
          const progressPercent = 70 + Math.round((i + 1) / sourceComics.length * 25);
          this.updateProgress(sessionId, {
            stage: 'syncing',
            progress: progressPercent,
            message: `Syncing comic ${i + 1}/${sourceComics.length}: ${comic.title}`,
            processedComics: i + 1
          });

          // Small delay to prevent overwhelming the system
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          errorCount++;
          errors.push({
            comicId: comic.id,
            comicTitle: comic.title,
            error: error instanceof Error ? error.message : 'Unknown error',
            source: comic.source
          });
        }
      }

      // Stage 5: Completed
      const duration = Math.round((Date.now() - startTime) / 1000);
      
      this.updateProgress(sessionId, {
        stage: 'completed',
        progress: 100,
        message: `Sync completed successfully! Synced ${syncedCount} comics.`,
        endTime: new Date()
      });

      // Save sync result to history
      const result: SyncResult = {
        sessionId,
        success: true,
        totalComics: sourceComics.length,
        syncedComics: syncedCount,
        skippedComics: skippedCount,
        errorComics: errorCount,
        errors,
        summary: {
          sourceComics: sourceComics.length,
          newComics: syncedCount, // Simplified - in real implementation, you'd check if comic already exists
          updatedComics: 0,
          duplicateComics: skippedCount
        },
        duration
      };

      this.syncHistory.unshift(result);
      
      // Keep only last 50 sync results
      if (this.syncHistory.length > 50) {
        this.syncHistory = this.syncHistory.slice(0, 50);
      }

    } catch (error) {
      const duration = Math.round((Date.now() - startTime) / 1000);
      
      this.updateProgress(sessionId, {
        stage: 'error',
        progress: 0,
        message: `Sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        endTime: new Date()
      });

      // Save failed sync result
      const result: SyncResult = {
        sessionId,
        success: false,
        totalComics: sourceComics.length,
        syncedComics: syncedCount,
        skippedComics: skippedCount,
        errorComics: errorCount + 1,
        errors: [
          ...errors,
          {
            comicId: 'system',
            comicTitle: 'System Error',
            error: error instanceof Error ? error.message : 'Unknown error',
            source: 'system'
          }
        ],
        summary: {
          sourceComics: sourceComics.length,
          newComics: syncedCount,
          updatedComics: 0,
          duplicateComics: skippedCount
        },
        duration
      };

      this.syncHistory.unshift(result);
    }
  }

  /**
   * Update sync progress
   */
  private updateProgress(sessionId: string, updates: Partial<SyncProgress>): void {
    const current = this.activeSessions.get(sessionId);
    if (current) {
      const updated = { ...current, ...updates };
      this.activeSessions.set(sessionId, updated);
    }
  }

  /**
   * Get sync progress for a session
   */
  async getSyncProgress(sessionId: string): Promise<SyncProgress | null> {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Get default sync settings
   */
  async getSettings(): Promise<SyncSettings> {
    // In a real implementation, you'd fetch this from database
    return {
      autoResolveConflicts: false,
      preferredSource: 'latest',
      syncInterval: 24,
      enableNotifications: true,
      backupBeforeSync: true
    };
  }

  /**
   * Save sync settings
   */
  async saveSettings(settings: SyncSettings): Promise<void> {
    // In a real implementation, you'd save this to database
    console.log('Saving sync settings:', settings);
  }

  /**
   * Get sync history
   */
  async getSyncHistory(limit: number = 10): Promise<SyncResult[]> {
    return this.syncHistory.slice(0, limit);
  }

  /**
   * Clean up completed sessions
   */
  cleanupSessions(): void {
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    for (const [sessionId, progress] of this.activeSessions.entries()) {
      if (progress.endTime && (now - progress.endTime.getTime()) > oneHour) {
        this.activeSessions.delete(sessionId);
      }
    }
  }
}
