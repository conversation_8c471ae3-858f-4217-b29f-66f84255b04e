import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SyncResult } from '../../models/sync-tracking.models';
import { SyncTrackingService } from '../../services/sync-tracking.service';
import { Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-sync-results',
  templateUrl: './sync-results.component.html',
  styleUrl: './sync-results.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class SyncResultsComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  @Output() newSync = new EventEmitter<void>();

  // Component state
  private readonly _result = signal<SyncResult | null>(null);
  private readonly _selectedTab = signal<'summary' | 'errors' | 'comics'>('summary');
  protected override readonly destroy$ = new Subject<void>();

  // Computed properties
  readonly result = computed(() => this._result());
  readonly selectedTab = computed(() => this._selectedTab());
  
  readonly hasResult = computed(() => !!this.result());
  readonly isSuccess = computed(() => this.result()?.success || false);
  readonly hasErrors = computed(() => (this.result()?.errors?.length || 0) > 0);
  
  readonly successRate = computed(() => {
    const result = this.result();
    if (!result || result.totalComics === 0) return 0;
    return Math.round((result.syncedComics / result.totalComics) * 100);
  });

  readonly formattedDuration = computed(() => {
    const duration = this.result()?.duration || 0;
    if (duration < 60) return `${duration}s`;
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    return `${minutes}m ${seconds}s`;
  });

  readonly summaryStats = computed(() => {
    const result = this.result();
    if (!result) return [];
    
    return [
      { label: 'Tổng truyện', value: result.totalComics, color: 'blue' },
      { label: 'Đã đồng bộ', value: result.syncedComics, color: 'green' },
      { label: 'Lỗi', value: result.errors.length, color: 'red' },
      { label: 'Thời gian', value: this.formattedDuration(), color: 'purple' }
    ];
  });

  readonly detailStats = computed(() => {
    const summary = this.result()?.summary;
    if (!summary) return [];
    
    return [
      { label: 'Tổng truyện', value: summary.comics, color: 'green' },
      { label: 'Mới thêm', value: summary.newComics, color: 'blue' },
      { label: 'Cập nhật', value: summary.updatedComics, color: 'purple' },
      { label: 'Xung đột', value: summary.conflictComics, color: 'red' }
    ];
  });


  constructor(
    private syncService: SyncTrackingService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.subscribeToResults();
  }

  override ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== SUBSCRIPTION MANAGEMENT =====

  private subscribeToResults(): void {
    this.syncService.syncResult$
      .pipe(takeUntil(this.destroy$))
      .subscribe(result => {
        this._result.set(result);
        this.safeMarkForCheck();
      });
  }


  // ===== ACTIONS =====

  startNewSync(): void {
    this.newSync.emit();
  }

  downloadReport(): void {
    const result = this.result();
    if (!result) return;

    const report = {
      timestamp: new Date().toISOString(),
      result: result
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sync-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  shareResults(): void {
    const result = this.result();
    if (!result) return;

    const text = `Đồng bộ truyện hoàn tất!\n` +
                `✅ ${result.syncedComics}/${result.totalComics} truyện\n` +
                `⏱️ ${this.formattedDuration()}\n` +
                `${result.errors.length > 0 ? `❌ ${result.errors.length} lỗi` : '🎉 Không có lỗi'}`;

    if (navigator.share) {
      navigator.share({
        title: 'Kết quả đồng bộ truyện',
        text: text
      });
    } else {
      navigator.clipboard.writeText(text);
      // Show toast notification
      console.log('Copied to clipboard');
    }
  }

  // ===== UTILITY METHODS =====

  getStatColor(color: string): string {
    const colors = {
      blue: 'text-blue-600 dark:text-blue-400',
      green: 'text-green-600 dark:text-green-400',
      orange: 'text-orange-600 dark:text-orange-400',
      purple: 'text-purple-600 dark:text-purple-400',
      red: 'text-red-600 dark:text-red-400'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  }

  getStatBgColor(color: string): string {
    const colors = {
      blue: 'bg-blue-100 dark:bg-blue-900/30',
      green: 'bg-green-100 dark:bg-green-900/30',
      orange: 'bg-orange-100 dark:bg-orange-900/30',
      purple: 'bg-purple-100 dark:bg-purple-900/30',
      red: 'bg-red-100 dark:bg-red-900/30'
    };
    return colors[color as keyof typeof colors] || colors.blue;
  }

  // ===== TRACKBY FUNCTIONS =====

  trackByLabel = (index: number, item: any): string => item.label;
}
