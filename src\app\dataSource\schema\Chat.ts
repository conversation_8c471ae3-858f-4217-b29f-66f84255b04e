export interface IConversation {
  id: string;
  name?: string;
  createdAt: string;
  updatedAt: string;
  messages: IMessage[];
  lastMessage?: IMessage;
  userConversations: IUserConversation[];
}

export interface IMessage {
  id: string;
  userId: number;
  content: string;
  conversationId: string;
  createdAt: string;
  user?: IChatUser;
}

export interface IUserConversation {
  id: number;
  userId: number;
  conversationId: string;
  createdAt: string;
  updatedAt: string;
  user?: IChatUser;
  conversation?: IConversation;
}

export interface IConversationList {
  id: string;
  name?: string;
  users: IChatUser[];
  lastMessage?: IMessage;
  updatedAt: string;
  isWorldChat: boolean;
}

export interface ICreateConversation {
  name?: string;
  userIds: number[];
}

export interface ISendMessage {
  content: string;
  conversationId?: string;
  targetUserId?: number;
  isWorldChat?: boolean;
}

export interface IMessagePage {
  messages: IMessage[];
  totalCount: number;
  page: number;
  pageSize: number;
  hasNextPage: boolean;
}

export interface IChatUser {
  id: number;
  email?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  dob?: string;
  createAt?: string;
  gender: number;
  token?: string;
  experience: number;
  status?: number;
  maxim?: string;
  typeLevel: number;
}
