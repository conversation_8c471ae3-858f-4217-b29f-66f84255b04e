import { Request, Response } from 'express';
import { CrawlerService } from './crawler.service';
import { SyncTrackingService } from './sync-tracking.service';

export class SyncTrackingController {
  private syncService = new SyncTrackingService();
  private crawlerService = new CrawlerService();

  /**
   * Test credentials for a specific source
   */
  async testCredentials(req: Request, res: Response) {
    try {
      const { site, credentials } = req.body;
      
      if (!site || !credentials) {
        res.status(400).json({
          success: false,
          error: 'Missing site or credentials'
        });
        return;
      }
      let newCredentials = JSON.parse(atob(credentials));
      const isValid = await this.crawlerService.testLogin(site, newCredentials);
      
      res.json({
        success: true,
        valid: isValid
      });
    } catch (error) {
      console.error('Test credentials error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to test credentials'
      });
    }
  }

  /**
   * Start sync process
   */
  async startSync(req: Request, res: Response): Promise<void> {
    try {
      const { credentials, settings } = req.body;
      
      if (!credentials || !settings) {
        res.status(400).json({
          success: false,
          error: 'Missing credentials or settings'
        });
        return;
      }

      // Start sync process
      const sessionId = await this.syncService.startSync(credentials, settings);
      
      res.json({
        success: true,
        sessionId,
        message: 'Sync process started'
      });
    } catch (error) {
      console.error('Start sync error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to start sync process'
      });
    }
  }

  /**
   * Get sync settings
   */
  async getSettings(req: Request, res: Response) {
    try {
      const settings = await this.syncService.getSettings();
      res.json({
        success: true,
        data: settings
      });
    } catch (error) {
      console.error('Get settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get settings'
      });
    }
  }

  /**
   * Save sync settings
   */
  async saveSettings(req: Request, res: Response) {
    try {
      const settings = req.body;
      await this.syncService.saveSettings(settings);
      
      res.json({
        success: true,
        message: 'Settings saved successfully'
      });
    } catch (error) {
      console.error('Save settings error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to save settings'
      });
    }
  }

  /**
   * Get sync history
   */
  // async getSyncHistory(req: Request, res: Response) {
  //   try {
  //     const limit = parseInt(req.query.limit as string) || 10;
  //     const history = await this.syncService.getSyncHistory(limit);
      
  //     res.json({
  //       success: true,
  //       data: history
  //     });
  //   } catch (error) {
  //     console.error('Get sync history error:', error);
  //     res.status(500).json({
  //       success: false,
  //       error: 'Failed to get sync history'
  //     });
  //   }
  // }

  /**
   * Get sync progress for real-time updates
   */
  async getSyncProgress(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;
      const progress = await this.syncService.getSyncProgress(sessionId);
      
      res.json({
        success: true,
        data: progress
      });
    } catch (error) {
      console.error('Get sync progress error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get sync progress'
      });
    }
  }
}
