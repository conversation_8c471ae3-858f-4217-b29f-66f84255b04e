import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { Router, RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ToastService } from '@services/toast.service';

interface NavigationItem {
  path: string;
  label: string;
  icon: string;
  description: string;
  badge?: number;
}

@Component({
  selector: 'app-user-profile-container',
  standalone: true,
  imports: [CommonModule, RouterOutlet, RouterLink, RouterLinkActive],
  templateUrl: './user-profile-container.component.html',
  styleUrl: './user-profile-container.component.scss',
})
export class UserProfileContainerComponent extends OptimizedBaseComponent implements OnInit {
  user: IUser | null = null;
  isLoading = true;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private accountService: AccountService,
    private toastService: ToastService,
    private router: Router,
  ) {
    super(cdr, platformId);
  }

  navigationItems: NavigationItem[] = [
    {
      path: '/tai-khoan/profile',
      label: 'Thông tin cá nhân',
      icon: 'user',
      description: 'Quản lý thông tin tài khoản'
    },
    {
      path: '/tai-khoan/daily-quests',
      label: 'Nhiệm vụ',
      icon: 'target',
      description: 'Nhiệm vụ hằng ngày và tuần',
      badge: 3
    },
    {
      path: '/tai-khoan/favorites',
      label: 'Truyện yêu thích',
      icon: 'heart',
      description: 'Danh sách truyện đã lưu',
      badge: 0
    },
    {
      path: '/tai-khoan/history',
      label: 'Lịch sử đọc',
      icon: 'clock',
      description: 'Truyện đã đọc gần đây'
    },
    {
      path: '/tai-khoan/stats',
      label: 'Thống kê',
      icon: 'chart',
      description: 'Thống kê hoạt động đọc truyện'
    },
    {
      path: '/tai-khoan/achievements',
      label: 'Thành tích',
      icon: 'trophy',
      description: 'Huy hiệu và thành tựu'
    },
    {
      path: '/tai-khoan/inventory',
      label: 'Kho đồ',
      icon: 'package',
      description: 'Quản lý vật phẩm'
    },
  ];

  ngOnInit() {
    this.runInBrowser(() => {
      this.loadUserData();
    });    
  }

  private loadUserData() {
    this.accountService.GetUserInfo().subscribe({
      next: (response: any) => {
        if (response.status) {
          this.user = response.data;
          this.updateNavigationBadges();
        }
        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.toastService.error('Bạn cần đăng nhập để truy cập trang này!');
        this.router.navigate(['/']); // Redirect to login page if user is not authenticated
      }
    });
  }

  private updateNavigationBadges() {
    // Update badges based on user data
    // This would be implemented based on actual data structure
  }

  trackByPath(index: number, item: NavigationItem): string {
    return item.path;
  }
}
