<!-- Sync Form Component -->
<div class="sync-form-container">
  <!-- Main Form -->
  <form class="sync-form" [formGroup]="syncForm" (ngSubmit)="onSubmit()">

    <!-- Step 1: Source Selection -->
    <div *ngIf="currentStep() === 'source'" class="form-step source-step">
      <div class="step-header">
        <div class="step-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
            <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
          </svg>
        </div>
        <div class="step-content">
          <h2 class="step-title">Chọn nguồn đồng bộ</h2>
          <p class="step-subtitle">Chọn website nguồn để lấy dữ liệu theo dõi truyện và đồng bộ vào website của bạn</p>
        </div>
      </div>

      <div class="source-selection">
        <!-- Source From -->
        <div class="source-group">
          <label class="source-label">Chọn nguồn đồng bộ</label>
          <div class="source-options">
            <div
              *ngFor="let option of sourceOptions(); trackBy: trackByValue"
              class="source-option"
              [class.selected]="syncForm.get('sourceFrom')?.value === option.value"
              (click)="syncForm.get('sourceFrom')?.setValue(option.value)"
            >
              <div class="option-icon" [attr.data-color]="option.color">
                <svg class="icon" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 12l2 2 4-4"/>
                </svg>
              </div>
              <div class="option-content">
                <h3 class="option-title">{{ option.label }}</h3>
                <p class="option-description">Lấy dữ liệu từ {{ option.label }} và đồng bộ vào website của bạn</p>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- Step Actions -->
      <div class="step-actions">
        <button
          type="button"
          class="action-button primary"
          [disabled]="!isSourceStepValid()"
          (click)="nextStep()"
        >
          <span class="button-text">Tiếp tục</span>
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="9 18 15 12 9 6"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Step 2: Method Selection -->
    <div *ngIf="currentStep() === 'method'" class="form-step method-step">
      <div class="step-header">
        <div class="step-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <div class="step-content">
          <h2 class="step-title">Chọn phương thức đồng bộ</h2>
          <p class="step-subtitle">Chọn cách thức để lấy dữ liệu từ {{ syncForm.get('sourceFrom')?.value }}</p>
        </div>
      </div>

      <div class="method-selection">
        <div class="method-options">
          <div
            *ngFor="let method of methodOptions(); trackBy: trackByValue"
            class="method-option"
            [class.selected]="syncForm.get('method')?.value === method.value"
            (click)="syncForm.get('method')?.setValue(method.value)"
          >
            <div class="method-icon" [attr.data-icon]="method.icon">
              <svg *ngIf="method.icon === 'user'" class="icon" viewBox="0 0 24 24">
                <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
              <svg *ngIf="method.icon === 'code'" class="icon" viewBox="0 0 24 24">
                <polyline points="16 18 22 12 16 6"/>
                <polyline points="8 6 2 12 8 18"/>
              </svg>
            </div>
            <div class="method-content">
              <h3 class="method-title">{{ method.label }}</h3>
              <p class="method-description">{{ method.description }}</p>
            </div>
          </div>
        </div>

        <!-- Method Info -->
        <div *ngIf="selectedMethod()" class="method-info">
          <div class="info-card">
            <div class="info-icon">
              <svg class="icon" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10"/>
                <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3"/>
                <path d="M12 17h.01"/>
              </svg>
            </div>
            <div class="info-content">
              <h4 class="info-title">
                {{ selectedMethod() === 'account' ? 'Đăng nhập tài khoản' : 'Chạy script console' }}
              </h4>
              <p class="info-text">
                <span *ngIf="selectedMethod() === 'account'">
                  Bạn sẽ cần nhập tên đăng nhập và mật khẩu của tài khoản {{ syncForm.get('sourceFrom')?.value }} để đồng bộ dữ liệu.
                </span>
                <span *ngIf="selectedMethod() === 'script'">
                  Bạn sẽ cần mở {{ syncForm.get('sourceFrom')?.value }}, đăng nhập, sau đó chạy script trong console để lấy dữ liệu.
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Step Actions -->
      <div class="step-actions">
        <button
          type="button"
          class="action-button secondary"
          (click)="prevStep()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="15 18 9 12 15 6"/>
          </svg>
          <span class="button-text">Quay lại</span>
        </button>

        <button
          type="button"
          class="action-button primary"
          [disabled]="!isMethodStepValid()"
          (click)="nextStep()"
        >
          <span class="button-text">Tiếp tục</span>
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="9 18 15 12 9 6"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Step 3: Credentials -->
    <div *ngIf="currentStep() === 'credentials'" class="form-step credentials-step">
      <div class="step-header">
        <div class="step-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
          </svg>
        </div>
        <div class="step-content">
          <h2 class="step-title">
            {{ showAccountForm() ? 'Thông tin đăng nhập' : 'Dữ liệu từ script' }}
          </h2>
          <p class="step-subtitle">
            <span *ngIf="showAccountForm()">
              Nhập thông tin tài khoản {{ syncForm.get('sourceFrom')?.value }} để truy cập dữ liệu
            </span>
            <span *ngIf="showScriptForm()">
              Dán dữ liệu đã lấy từ script console của {{ syncForm.get('sourceFrom')?.value }}
            </span>
          </p>
        </div>
      </div>

      <div class="credentials-form">
        <!-- Account Form -->
        <div *ngIf="showAccountForm()" class="account-form">
          <div class="form-group">
            <label class="form-label">Tên đăng nhập</label>
            <div class="input-wrapper">
              <input
                type="text"
                class="form-input"
                formControlName="username"
                placeholder="Nhập tên đăng nhập"
                [class.error]="syncForm.get('username')?.invalid && syncForm.get('username')?.touched"
              />
              <svg class="input-icon" viewBox="0 0 24 24">
                <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
              </svg>
            </div>
            <div class="form-error" *ngIf="syncForm.get('username')?.invalid && syncForm.get('username')?.touched">
              Tên đăng nhập phải có ít nhất 3 ký tự
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Mật khẩu</label>
            <div class="input-wrapper">
              <input
                [type]="showPassword() ? 'text' : 'password'"
                class="form-input"
                formControlName="password"
                placeholder="Nhập mật khẩu"
                [class.error]="syncForm.get('password')?.invalid && syncForm.get('password')?.touched"
              />
              <button
                type="button"
                class="input-action"
                (click)="togglePasswordVisibility()"
              >
                <svg class="action-icon" viewBox="0 0 24 24">
                  <path *ngIf="!showPassword()" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                  <circle *ngIf="!showPassword()" cx="12" cy="12" r="3"/>
                  <path *ngIf="showPassword()" d="M17.94 17.94A10.07 10.07 0 0112 20c-7 0-11-8-11-8a18.45 18.45 0 015.06-5.94M9.9 4.24A9.12 9.12 0 0112 4c7 0 11 8 11 8a18.5 18.5 0 01-2.16 3.19m-6.72-1.07a3 3 0 11-4.24-4.24"/>
                  <line *ngIf="showPassword()" x1="1" y1="1" x2="23" y2="23"/>
                </svg>
              </button>
            </div>
            <div class="form-error" *ngIf="syncForm.get('password')?.invalid && syncForm.get('password')?.touched">
              Mật khẩu phải có ít nhất 6 ký tự
            </div>
          </div>
        </div>

        <!-- Script Form -->
        <div *ngIf="showScriptForm()" class="script-form">
          <div class="script-instructions">
            <div class="instruction-card">
              <div class="instruction-header">
                <svg class="instruction-icon" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3"/>
                  <path d="M12 17h.01"/>
                </svg>
                <h4 class="instruction-title">Hướng dẫn lấy dữ liệu</h4>
              </div>
              <ol class="instruction-steps">
                <li *ngFor="let instruction of scriptInstructions(); trackBy: trackByIndex">
                  {{ instruction }}
                </li>
              </ol>
              <div class="script-code">
                <div class="script-header">
                  <span class="script-title">Script Console</span>
                  <button
                    type="button"
                    class="copy-script-button"
                    (click)="copyScript()"
                  >
                    <svg class="copy-icon" viewBox="0 0 24 24">
                      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                      <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"/>
                    </svg>
                    Copy
                  </button>
                </div>
                <pre><code>{{ scriptTemplate() }}</code></pre>
              </div>
              <p class="instruction-note">
                Script sẽ tự động lấy dữ liệu và hiển thị kết quả. Copy kết quả và dán vào ô bên dưới.
              </p>
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">Dữ liệu từ script</label>
            <div class="textarea-wrapper">
              <textarea
                class="form-textarea"
                formControlName="scriptData"
                placeholder="Dán dữ liệu JSON từ script console vào đây..."
                rows="8"
                [class.error]="syncForm.get('scriptData')?.invalid && syncForm.get('scriptData')?.touched"
              ></textarea>
            </div>
            <div class="form-error" *ngIf="syncForm.get('scriptData')?.invalid && syncForm.get('scriptData')?.touched">
              Vui lòng dán dữ liệu từ script console
            </div>
          </div>
        </div>

        <button
          type="button"
          class="test-button"
          (click)="testCredentials()"
          [disabled]="isLoading() || !isCredentialsStepValid()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span class="button-text">
            {{ showAccountForm() ? 'Kiểm tra kết nối' : 'Kiểm tra dữ liệu' }}
          </span>
        </button>

        <div *ngIf="credentialsValid()" class="validation-success">
          <svg class="success-icon" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span class="success-text">
            {{ showAccountForm() ? 'Thông tin đăng nhập hợp lệ!' : 'Dữ liệu script hợp lệ!' }}
          </span>
        </div>
      </div>

      <!-- Step Actions -->

      <div class="step-actions">
        <button
          type="button"
          class="action-button secondary"
          (click)="prevStep()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="15 18 9 12 15 6"/>
          </svg>
          <span class="button-text">Quay lại</span>
        </button>

        <button
          type="submit"
          class="action-button primary"
          [disabled]="!canStartSync()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
            <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
          </svg>
          <span class="button-text">Bắt đầu đồng bộ</span>
        </button>
      </div>
    </div>


  </form>
</div>