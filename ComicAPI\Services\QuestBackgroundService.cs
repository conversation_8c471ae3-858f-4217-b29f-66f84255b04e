using ComicApp.Reposibility;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace ComicApp.Services
{
    public class QuestBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<QuestBackgroundService> _logger;
        private readonly TimeSpan _checkInterval = TimeSpan.FromMinutes(5); // Check every 5 minutes

        public QuestBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<QuestBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Quest Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessQuestMaintenance();
                    await Task.Delay(_checkInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in Quest Background Service");
                    // Continue running even if there's an error
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }

            _logger.LogInformation("Quest Background Service stopped");
        }

        private async Task ProcessQuestMaintenance()
        {
            using var scope = _serviceProvider.CreateScope();
            var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();

            try
            {
                var now = DateTime.UtcNow;
                
                // Check if it's time for daily reset (midnight UTC)
                if (ShouldResetDaily(now))
                {
                    _logger.LogInformation("Starting daily quest reset at {Time}", now);
                    await questRepository.ResetDailyQuestsAsync();
                    await questRepository.ExpireOldQuestsAsync();
                    _logger.LogInformation("Daily quest reset completed");
                }

                // Check if it's time for weekly reset (Monday midnight UTC)
                if (ShouldResetWeekly(now))
                {
                    _logger.LogInformation("Starting weekly quest reset at {Time}", now);
                    await questRepository.ResetWeeklyQuestsAsync();
                    _logger.LogInformation("Weekly quest reset completed");
                }

                // Always expire old quests
                await questRepository.ExpireOldQuestsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during quest maintenance");
            }
        }

        private bool ShouldResetDaily(DateTime now)
        {
            // Reset daily quests at midnight UTC
            var lastMidnight = now.Date;
            var nextMidnight = lastMidnight.AddDays(1);
            
            // Check if we're within 5 minutes of midnight
            var timeSinceMidnight = now - lastMidnight;
            return timeSinceMidnight <= TimeSpan.FromMinutes(5);
        }

        private bool ShouldResetWeekly(DateTime now)
        {
            // Reset weekly quests on Monday at midnight UTC
            var dayOfWeek = now.DayOfWeek;
            if (dayOfWeek != DayOfWeek.Monday)
                return false;

            var timeSinceMidnight = now.TimeOfDay;
            return timeSinceMidnight <= TimeSpan.FromMinutes(5);
        }
    }

    public class QuestSchedulerService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<QuestSchedulerService> _logger;
        private Timer? _dailyTimer;
        private Timer? _weeklyTimer;
        private Timer? _maintenanceTimer;

        public QuestSchedulerService(
            IServiceProvider serviceProvider,
            ILogger<QuestSchedulerService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Quest Scheduler Service starting");

            // Schedule daily reset at midnight UTC
            ScheduleDailyReset();
            
            // Schedule weekly reset on Monday midnight UTC
            ScheduleWeeklyReset();
            
            // Schedule maintenance every hour
            ScheduleMaintenance();

            _logger.LogInformation("Quest Scheduler Service started");
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Quest Scheduler Service stopping");

            _dailyTimer?.Dispose();
            _weeklyTimer?.Dispose();
            _maintenanceTimer?.Dispose();

            _logger.LogInformation("Quest Scheduler Service stopped");
            return Task.CompletedTask;
        }

        private void ScheduleDailyReset()
        {
            var now = DateTime.UtcNow;
            var nextMidnight = now.Date.AddDays(1);
            var timeUntilMidnight = nextMidnight - now;

            _dailyTimer = new Timer(async _ => await ExecuteDailyReset(), null, timeUntilMidnight, TimeSpan.FromDays(1));
            
            _logger.LogInformation("Daily reset scheduled for {Time} UTC", nextMidnight);
        }

        private void ScheduleWeeklyReset()
        {
            var now = DateTime.UtcNow;
            var nextMonday = GetNextMonday(now);
            var timeUntilMonday = nextMonday - now;

            _weeklyTimer = new Timer(async _ => await ExecuteWeeklyReset(), null, timeUntilMonday, TimeSpan.FromDays(7));
            
            _logger.LogInformation("Weekly reset scheduled for {Time} UTC", nextMonday);
        }

        private void ScheduleMaintenance()
        {
            // Run maintenance every hour
            _maintenanceTimer = new Timer(async _ => await ExecuteMaintenance(), null, TimeSpan.Zero, TimeSpan.FromHours(1));
            
            _logger.LogInformation("Maintenance scheduled to run every hour");
        }

        private async Task ExecuteDailyReset()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();

                _logger.LogInformation("Executing daily quest reset");
                await questRepository.ResetDailyQuestsAsync();
                await questRepository.ExpireOldQuestsAsync();
                _logger.LogInformation("Daily quest reset completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during daily quest reset");
            }
        }

        private async Task ExecuteWeeklyReset()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();

                _logger.LogInformation("Executing weekly quest reset");
                await questRepository.ResetWeeklyQuestsAsync();
                _logger.LogInformation("Weekly quest reset completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during weekly quest reset");
            }
        }

        private async Task ExecuteMaintenance()
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var questRepository = scope.ServiceProvider.GetRequiredService<IQuestRepository>();

                // Expire old quests
                await questRepository.ExpireOldQuestsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during quest maintenance");
            }
        }

        private DateTime GetNextMonday(DateTime date)
        {
            var daysUntilMonday = ((int)DayOfWeek.Monday - (int)date.DayOfWeek + 7) % 7;
            if (daysUntilMonday == 0 && date.TimeOfDay > TimeSpan.Zero)
            {
                daysUntilMonday = 7; // If it's Monday but not midnight, schedule for next Monday
            }
            
            var nextMonday = date.Date.AddDays(daysUntilMonday);
            return nextMonday;
        }
    }
}
