
import axios from 'axios';
import * as cheerio from 'cheerio';

const LOGIN_URL = 'https://nettruyenrr.com/Secure/Login.aspx'; // Thay URL thực tế
(async () => {
  const client = axios.create({
    withCredentials: true,
    headers: {
      'User-Agent': 'Mozilla/5.0',
    }
  });

  // 1. T<PERSON>y cập trang login để lấy cookie + hidden fields
  
  const getResp = await client.get(LOGIN_URL);
  const cookie = getResp.headers['set-cookie']; // Lấy cookie

  const $ = cheerio.load(getResp.data);
  const token = $('input[name="_token"]').val();
  

  // 2. Tạo dữ liệu để submit login
  const formData = new URLSearchParams();
  formData.append('_token', token);
  formData.append('email', '<EMAIL>');
  formData.append('password', 'quanhieuchien');
  formData.append('ctl00$mainContent$login1$LoginCtrl$RememberMe', 'on'); // nếu cần

  // 3. Gửi POST để login
  const postResp = await client.post(LOGIN_URL, formData.toString(), {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Cookie': cookie.join('; ')
    },
    maxRedirects: 0, // nếu bạn muốn kiểm tra login thành công hay không
    validateStatus: status => status < 400 // để tránh axios ném lỗi 302
  });

  const cookies = postResp.headers['set-cookie'];

  const res = await client.get('https://nettruyenrr.com/theo-doi', {
    headers: {
      'Cookie': cookies.join('; ')
    }
  });

    const $1 = cheerio.load(res.data);

    const comics = [];

    $1('.comics-followed-page .items .item').each((_, el) => {
      const name = $(el).find('figcaption h3 a').text().trim();
      const url = $(el).find('figcaption h3 a').attr('href');

      if (name && url) {
        comics.push({ name, url });
      }
    });

    console.log(comics);


  const isLoginSuccess = res.data.includes('Trang cá nhân');
  // 4. Kiểm tra kết quả
  if (isLoginSuccess) {
    console.log('✅ Đăng nhập thành công!');
    console.log(res.headers['set-cookie']);
  } else {
    console.log('❌ Đăng nhập thất bại.');
    console.log(res.headers);
  }
})();
