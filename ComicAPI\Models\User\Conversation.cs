using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicApp.Models
{
    public class Conversation
    {
        [Key, Column("id")]
        public Guid ID { get; set; } = Guid.NewGuid();

        [<PERSON><PERSON><PERSON><PERSON>(255), Column("name")]
        public string? Name { get; set; }

        [Required, <PERSON>umn("user_id_a")]
        public int UserIdA { get; set; }

        [Required, <PERSON>umn("agent_id")]
        public int AgentId { get; set; }

        [Required, Column("user_id_b")]
        public int UserIdB { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [Column("last_message_id")]
        public Guid? LastMessageId { get; set; }

        // Navigation properties
        public User? UserA { get; set; }
        public User? UserB { get; set; }
        public User? Agent { get; set; }
        public List<Message> Messages { get; set; } = new List<Message>();
    }
}
