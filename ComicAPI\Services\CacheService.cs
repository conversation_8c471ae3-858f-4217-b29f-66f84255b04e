using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using System.Text.Json;

namespace ComicAPI.Services
{
    /// <summary>
    /// High-performance caching service with fallback to memory cache
    /// </summary>
    public class CacheService : ICacheService
    {
        private readonly IDistributedCache? _distributedCache;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<CacheService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public CacheService(
            IMemoryCache memoryCache,
            ILogger<CacheService> logger,
            IDistributedCache? distributedCache = null)
        {
            _memoryCache = memoryCache;
            _distributedCache = distributedCache;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                // Try distributed cache first
                if (_distributedCache != null)
                {
                    var distributedValue = await _distributedCache.GetStringAsync(key);
                    if (!string.IsNullOrEmpty(distributedValue))
                    {
                        return JsonSerializer.Deserialize<T>(distributedValue, _jsonOptions);
                    }
                }

                // Fallback to memory cache
                return _memoryCache.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache value for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            if (value == null) return;

            var defaultExpiration = expiration ?? TimeSpan.FromMinutes(30);

            try
            {
                // Set in distributed cache
                if (_distributedCache != null)
                {
                    var serializedValue = JsonSerializer.Serialize(value, _jsonOptions);
                    var options = new DistributedCacheEntryOptions
                    {
                        AbsoluteExpirationRelativeToNow = defaultExpiration
                    };
                    await _distributedCache.SetStringAsync(key, serializedValue, options);
                }

                // Set in memory cache as backup
                var memoryCacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = defaultExpiration,
                    Size = 1 // For memory management
                };
                _memoryCache.Set(key, value, memoryCacheOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache value for key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                if (_distributedCache != null)
                {
                    await _distributedCache.RemoveAsync(key);
                }
                _memoryCache.Remove(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache value for key: {Key}", key);
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            // Note: This is a simplified implementation
            // For Redis, you would use SCAN command with pattern matching
            _logger.LogWarning("RemoveByPatternAsync not fully implemented for distributed cache");
            await Task.CompletedTask;
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (_distributedCache != null)
                {
                    var value = await _distributedCache.GetStringAsync(key);
                    return !string.IsNullOrEmpty(value);
                }
                return _memoryCache.TryGetValue(key, out _);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache existence for key: {Key}", key);
                return false;
            }
        }

        public async Task<T> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null) where T : class
        {
            var cachedValue = await GetAsync<T>(key);
            if (cachedValue != null)
            {
                return cachedValue;
            }

            var value = await factory();
            if (value != null)
            {
                await SetAsync(key, value, expiration);
            }

            return value;
        }
    }
}
