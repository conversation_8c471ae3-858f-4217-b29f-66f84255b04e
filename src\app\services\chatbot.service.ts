import { Injectable } from '@angular/core';

import { HttpClient } from '@angular/common/http';
import { EventSourcePolyfill } from "event-source-polyfill";

import { AccountService } from './account.service';
@Injectable({
    providedIn: 'root',
})
export class ChatbotService {
    constructor(private httpClient: HttpClient, private auth: AccountService) { }
    Chat(query: string, use_context = true, k = 5): EventSourcePolyfill | undefined {
        if (!this.auth.isAuthenticated()) return;
        const params = new URLSearchParams({ user_question: query});
        const url = `http://localhost:5080/api/chat/chatbot?${params}`;


        return new EventSourcePolyfill(url, {

            headers: {
                'Authorization': `Bearer ${this.auth.getAuthorizationToken()}`
            }
        }); // Tạo EventSource

    }

    ChatNew(query: string) {
        return this.httpClient.request('GET', `http://localhost:5080/api/chat/chatbot?user_question=${query}`, {
            reportProgress: true,
            observe: 'events',
            responseType: 'text'  // hoặc 'blob' nếu bạn muốn stream nhị phân
        })
    }

}
