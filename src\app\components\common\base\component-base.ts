import { 
  Component, 
  Inject, 
  PLATFORM_ID,
  ChangeDetectionStrategy 
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Component({
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export abstract class BaseComponent  {
  protected isBrowser: boolean;

  constructor(
    @Inject(PLATFORM_ID) protected platformId: object,
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  protected runInBrowser(callback: () => void): void {
    if (this.isBrowser) {
      callback();
    }
  }

  protected debounce(func: Function, delay: number): Function {
    let timeoutId: any;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  }

  /**
   * Throttled function execution
   */
  protected throttle(func: Function, delay: number): Function {
    let lastCall = 0;
    return (...args: any[]) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        func.apply(this, args);
      }
    };
  }


}
