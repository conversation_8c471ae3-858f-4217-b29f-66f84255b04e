import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import {
  SyncConflict,
  SyncCredentials,
  SyncHistory,
  SyncProgress,
  SyncResult,
  SyncSettings,
  SyncStage,
  TrackedComic
} from '../models/sync-tracking.models';
import { SyncTrackingMockService } from './sync-tracking-mock.service';

@Injectable({
  providedIn: 'root'
})
export class SyncTrackingService {
  private readonly API_BASE = '/api/sync-tracking';
  private readonly USE_MOCK = true; // Set to false for real API

  // State management
  private progressSubject = new BehaviorSubject<SyncProgress>({
    stage: SyncStage.IDLE,
    progress: 0,
    message: 'Ready to sync'
  });

  private syncResultSubject = new BehaviorSubject<SyncResult | null>(null);
  private conflictsSubject = new BehaviorSubject<SyncConflict[]>([]);

  // Public observables
  public progress$ = this.progressSubject.asObservable();
  public syncResult$ = this.syncResultSubject.asObservable();
  public conflicts$ = this.conflictsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private mockService: SyncTrackingMockService
  ) {}

  // ===== MAIN SYNC METHODS =====

  /**
   * Start sync process with credentials
   */
  startSync(credentials: SyncCredentials, settings: SyncSettings): Observable<SyncResult> {
    this.updateProgress(SyncStage.CONNECTING, 0, 'Connecting to websites...');

    if (this.USE_MOCK) {
      return this.simulateMockSync(credentials, settings);
    }

    const payload = {
      credentials: this.encryptCredentials(credentials),
      settings
    };
    console.log('Payload:', payload);
    
    return this.http.post<SyncResult>(`${this.API_BASE}/start`, payload).pipe(
      tap(result => {
        this.syncResultSubject.next(result);
        this.updateProgress(SyncStage.COMPLETED, 100, 'Sync completed successfully!');
      }),
      catchError(error => {
        this.updateProgress(SyncStage.ERROR, 0, `Sync failed: ${error.message}`);
        return throwError(() => error);
      })
    );
  }

  /**
   * Get tracked comics from specific source
   */
  getTrackedComics(source: 'nettruyen' | 'truyenqq', credentials: any): Observable<TrackedComic[]> {
    const payload = {
      source,
      credentials: this.encryptCredentials(credentials)
    };

    return this.http.post<TrackedComic[]>(`${this.API_BASE}/fetch-comics`, payload).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Resolve sync conflicts
   */
  resolveConflicts(conflicts: SyncConflict[]): Observable<SyncResult> {
    return this.http.post<SyncResult>(`${this.API_BASE}/resolve-conflicts`, { conflicts }).pipe(
      tap(result => this.syncResultSubject.next(result)),
      catchError(this.handleError)
    );
  }

  /**
   * Test website credentials
   */
  testCredentials(site: 'nettruyen' | 'truyenqq', credentials: any): Observable<boolean> {
    if (this.USE_MOCK) {
      return this.mockService.testCredentials(site, credentials);
    }

    const payload = {
      site,
      credentials: this.encryptCredentials(credentials)
    };

    return this.http.post<{ valid: boolean }>(`${this.API_BASE}/test-credentials`, payload).pipe(
      map(response => response.valid),
      catchError(() => throwError(() => new Error('Failed to test credentials')))
    );
  }

  // ===== SETTINGS & HISTORY =====

  /**
   * Save sync settings
   */
  saveSettings(settings: SyncSettings): Observable<void> {
    return this.http.post<void>(`${this.API_BASE}/settings`, settings).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get sync settings
   */
  getSettings(): Observable<SyncSettings> {
    if (this.USE_MOCK) {
      return this.mockService.getSettings();
    }

    return this.http.get<SyncSettings>(`${this.API_BASE}/settings`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get sync history
   */
  getSyncHistory(limit: number = 10): Observable<SyncHistory[]> {
    return this.http.get<SyncHistory[]>(`${this.API_BASE}/history?limit=${limit}`).pipe(
      catchError(this.handleError)
    );
  }

  // ===== UTILITY METHODS =====

  /**
   * Update sync progress
   */
  private updateProgress(stage: SyncStage, progress: number, message: string, currentSite?: string): void {
    this.progressSubject.next({
      stage,
      progress,
      message,
      currentSite,
      currentAction: this.getActionForStage(stage)
    });
  }

  /**
   * Get action description for stage
   */
  private getActionForStage(stage: SyncStage): string {
    const actions = {
      [SyncStage.IDLE]: 'Waiting',
      [SyncStage.CONNECTING]: 'Connecting',
      [SyncStage.FETCHING_NETTRUYEN]: 'Fetching NetTruyen data',
      [SyncStage.FETCHING_TRUYENQQ]: 'Fetching TruyenQQ data',
      [SyncStage.COMPARING]: 'Comparing data',
      [SyncStage.SYNCING]: 'Syncing changes',
      [SyncStage.COMPLETED]: 'Completed',
      [SyncStage.ERROR]: 'Error occurred'
    };
    return actions[stage] || 'Processing';
  }

  /**
   * Encrypt credentials for secure transmission
   */
  private encryptCredentials(credentials: any): string {
    // In production, use proper encryption
    return btoa(JSON.stringify(credentials));
  }

  /**
   * Handle HTTP errors
   */
  private handleError = (error: any) => {
    console.error('Sync service error:', error);
    return throwError(() => new Error(error.message || 'An error occurred'));
  };

  // ===== PUBLIC UTILITY METHODS =====

  /**
   * Reset sync state
   */
  resetSync(): void {
    this.progressSubject.next({
      stage: SyncStage.IDLE,
      progress: 0,
      message: 'Ready to sync'
    });
    this.syncResultSubject.next(null);
    this.conflictsSubject.next([]);
  }

  /**
   * Get current progress
   */
  getCurrentProgress(): SyncProgress {
    return this.progressSubject.value;
  }

  /**
   * Check if sync is in progress
   */
  isSyncInProgress(): boolean {
    const stage = this.progressSubject.value.stage;
    return stage !== SyncStage.IDLE && stage !== SyncStage.COMPLETED && stage !== SyncStage.ERROR;
  }

  // ===== MOCK SIMULATION METHODS =====

  /**
   * Simulate mock sync process with realistic progress updates
   */
  private simulateMockSync(credentials: SyncCredentials, settings: SyncSettings): Observable<SyncResult> {
    const stages = [
      { stage: SyncStage.CONNECTING, duration: 2000, message: 'Connecting to websites...', progress: 10 },
      { stage: SyncStage.FETCHING_NETTRUYEN, duration: 3000, message: 'Fetching NetTruyen data...', progress: 30 },
      { stage: SyncStage.FETCHING_TRUYENQQ, duration: 3000, message: 'Fetching TruyenQQ data...', progress: 50 },
      { stage: SyncStage.COMPARING, duration: 2000, message: 'Comparing data...', progress: 70 },
      { stage: SyncStage.SYNCING, duration: 4000, message: 'Syncing changes...', progress: 90 },
      { stage: SyncStage.COMPLETED, duration: 500, message: 'Sync completed successfully!', progress: 100 }
    ];

    return new Observable<SyncResult>(observer => {
      let currentStageIndex = 0;

      const processNextStage = () => {
        if (currentStageIndex >= stages.length) {
          // Complete with mock result
          const result = this.generateMockResult();
          this.syncResultSubject.next(result);
          observer.next(result);
          observer.complete();
          return;
        }

        const currentStage = stages[currentStageIndex];
        const startTime = Date.now();

        // Update progress for current stage
        this.updateProgress(currentStage.stage, currentStage.progress, currentStage.message);

        // Simulate stage duration
        setTimeout(() => {
          currentStageIndex++;
          processNextStage();
        }, currentStage.duration);
      };

      processNextStage();
    });
  }

  /**
   * Generate mock sync result
   */
  private generateMockResult(): SyncResult {
    return {
      success: true,
      totalComics: 5,
      syncedComics: 4,
      errors: [
        {
          comicId: 'nt-004',
          comicTitle: 'Bleach',
          error: 'Chapter data not found',
          source: 'nettruyen'
        }
      ],
      summary: {
        nettruyenComics: 3,
        truyenqqComics: 3,
        newComics: 2,
        updatedComics: 2,
        conflictComics: 1
      },
      duration: 14
    };
  }
}
