import axios, { AxiosInstance } from 'axios';
import * as cheerio from 'cheerio';

export interface TrackedComic {
  id: string;
  title: string;
  url: string;
  lastReadChapter: string;
  lastReadDate: Date;
  totalChapters: number;
  status: 'reading' | 'completed' | 'dropped' | 'plan_to_read';
  thumbnail: string;
  source: 'nettruyen' | 'truyenqq';
}

export class CrawlerService {
  private nettruyenClient: AxiosInstance;
  private truyenqqClient: AxiosInstance;

  constructor() {
    // NetTruyen client
    this.nettruyenClient = axios.create({
      baseURL: 'https://nettruyenrr.com',
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
        withCredentials: true,

    });

    // TruyenQQ client
    this.truyenqqClient = axios.create({
      baseURL: 'https://truyenqqto.com',
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      },
      withCredentials: true,
    });
  }

  /**
   * Test login credentials for a specific site
   */
  async testLogin(site: 'nettruyen' | 'truyenqq', credentials: { username: string; password: string }): Promise<boolean> {
    console.log
    (`Testing login for ${site} with credentials:`, credentials);
    try {
      if (site === 'nettruyen') {
        return await this.testNettruyenLogin(credentials);
      } else {
        return await this.testTruyenqqLogin(credentials);
      }
    } catch (error) {
      console.error(`Login test failed for ${site}:`, error);
      return false;
    }
  }

  /**
   * Test NetTruyen login
   */
  private async testNettruyenLogin(credentials: { username: string; password: string }): Promise<boolean> {
    try {
      // First, get the login page to extract CSRF token
      const loginPageResponse = await this.nettruyenClient.get('/Secure/Login.aspx');
      const $ = cheerio.load(loginPageResponse.data);
      const token = $('input[name="_token"]').val() as string;
      if (!token) {
        throw new Error('Could not extract CSRF token from NetTruyen login page');
      }

      // Create form data for NetTruyen login
      const formData = new URLSearchParams();
      formData.append('_token', token);
      formData.append('email', '<EMAIL>');
      formData.append('password', 'quanhieuchien');
      formData.append('ctl00$mainContent$login1$LoginCtrl$RememberMe', 'on'); // nếu cần
      // Attempt login
      const loginResponse = await this.nettruyenClient.post('/Secure/Login.aspx', formData.toString(), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'cookie':  loginPageResponse.headers['set-cookie']
        },
        validateStatus: (status) => status < 400,
        maxRedirects: 0,
      });
      const cookies = loginResponse.headers['set-cookie'];
      console.log(cookies);
      let res = await this.nettruyenClient.get("/theo-doi")

      
      // Check if login was successful
      // NetTruyen typically redirects on successful login
      return loginResponse.status === 302 || loginResponse.data.includes('Đăng xuất') || loginResponse.data.includes('logout');
    } catch (error) {
      console.error('NetTruyen login test error:', error);
      return false;
    }
  }

  /**
   * Test TruyenQQ login
   */
  private async testTruyenqqLogin(credentials: { username: string; password: string }): Promise<boolean> {
    try {
      // TruyenQQ login with form data
      const formData = new URLSearchParams();
      formData.append('email', credentials.username);
      formData.append('password', credentials.password);
      formData.append('expire', '1');

      const loginResponse = await this.truyenqqClient.post('/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Referer': 'https://truyenqqto.com/login'
        },
        maxRedirects: 0,
        validateStatus: (status) => status < 400
      });

      // Check if login was successful
      // TruyenQQ typically redirects on successful login or returns success response
      return loginResponse.status === 302 || 
             loginResponse.data.includes('success') || 
             loginResponse.data.includes('dashboard') ||
             loginResponse.data.includes('Đăng xuất');
    } catch (error) {
      console.error('TruyenQQ login test error:', error);
      return false;
    }
  }

  /**
   * Fetch tracked comics from NetTruyen
   */
  async fetchNettruyenComics(credentials: { username: string; password: string }): Promise<TrackedComic[]> {
    try {
      // Login first
      const loginSuccess = await this.testNettruyenLogin(credentials);
      if (!loginSuccess) {
        throw new Error('Failed to login to NetTruyen');
      }

      // Fetch user's followed comics
      const followedResponse = await this.nettruyenClient.get('/Account/Followed');
      const $ = cheerio.load(followedResponse.data);
      
      const comics: TrackedComic[] = [];
      
      $('.item').each((index, element) => {
        const $item = $(element);
        const title = $item.find('.title a').text().trim();
        const url = $item.find('.title a').attr('href') || '';
        const thumbnail = $item.find('img').attr('src') || '';
        const lastChapter = $item.find('.chapter a').text().trim();
        
        if (title && url) {
          comics.push({
            id: `nt-${index}`,
            title,
            url: url.replace('https://www.nettruyen.com', ''),
            lastReadChapter: lastChapter,
            lastReadDate: new Date(),
            totalChapters: 0, // Will be fetched separately if needed
            status: 'reading',
            thumbnail,
            source: 'nettruyen'
          });
        }
      });

      return comics;
    } catch (error) {
      console.error('Error fetching NetTruyen comics:', error);
      throw error;
    }
  }

  /**
   * Fetch tracked comics from TruyenQQ
   */
  async fetchTruyenqqComics(credentials: { username: string; password: string }): Promise<TrackedComic[]> {
    try {
      // Login first
      const loginSuccess = await this.testTruyenqqLogin(credentials);
      if (!loginSuccess) {
        throw new Error('Failed to login to TruyenQQ');
      }

      // Fetch user's followed comics
      const followedResponse = await this.truyenqqClient.get('/user/followed');
      const $ = cheerio.load(followedResponse.data);
      
      const comics: TrackedComic[] = [];
      
      $('.story-item').each((index, element) => {
        const $item = $(element);
        const title = $item.find('.story-name a').text().trim();
        const url = $item.find('.story-name a').attr('href') || '';
        const thumbnail = $item.find('img').attr('src') || '';
        const lastChapter = $item.find('.story-chapter a').text().trim();
        
        if (title && url) {
          comics.push({
            id: `tqq-${index}`,
            title,
            url: url.replace('https://truyenqqto.com', ''),
            lastReadChapter: lastChapter,
            lastReadDate: new Date(),
            totalChapters: 0, // Will be fetched separately if needed
            status: 'reading',
            thumbnail,
            source: 'truyenqq'
          });
        }
      });

      return comics;
    } catch (error) {
      console.error('Error fetching TruyenQQ comics:', error);
      throw error;
    }
  }

  /**
   * Sync comic to local database
   */
  async syncComicToLocal(comic: TrackedComic): Promise<boolean> {
    try {
      // Here you would implement the logic to save comic to your local database
      // This is a placeholder implementation
      console.log(`Syncing comic to local DB: ${comic.title}`);

      // Example: Save to your local comic database
      // You can implement this based on your database structure
      const localComic = {
        title: comic.title,
        slug: this.generateSlug(comic.title),
        description: '', // You might want to fetch this
        thumbnail: comic.thumbnail,
        status: comic.status,
        source_url: comic.url,
        source_site: comic.source,
        last_read_chapter: comic.lastReadChapter,
        total_chapters: comic.totalChapters,
        created_at: new Date(),
        updated_at: new Date()
      };

      // TODO: Implement actual database save
      // await this.localComicService.saveComic(localComic);

      return true;
    } catch (error) {
      console.error('Error syncing comic to local:', error);
      return false;
    }
  }

  /**
   * Generate slug from title
   */
  private generateSlug(title: string): string {
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
  }
}
