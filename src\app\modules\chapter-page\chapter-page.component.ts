import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  Renderer2,
  ViewChild,
} from '@angular/core';

import { DOCUMENT } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { FadeInDirective } from '@directives/fade-in.directive';
import { Chapter, ChapterPage, Comic, IServiceResponse, Page, UserExpType } from '@schema';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { StorageService } from '@services/storage.service';
import { UrlService } from '@services/url.service';
import { ServiceUtility } from '@services/utils.service';
import { ChapterServer } from 'src/app/dataSource/schema/ChapterServer';
import { throttle } from 'src/app/utils';
export enum StickyState {
  NoneSticky = 0,
  StickyTop = 1,
  StickyBottom = 2,
  StickyInvisible = 3,
}
@Component({
  selector: 'app-chapter',
  templateUrl: './chapter-page.component.html',
  styleUrl: './chapter-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChapterPageComponent extends OptimizedBaseComponent
  implements AfterViewInit, AfterViewChecked, OnDestroy, OnInit {
  onLoad($event: Event) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onload = null;
    imageEle.classList.remove('min-h-48');
  }
  onError($event: ErrorEvent) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onerror = null;
    imageEle.classList.add('hidden');
  }
  listImg?: Page[];
  listChapterServerIds: number[] = [];
  comic!: Comic;
  mainChapter!: ChapterPage;
  listChapters: Chapter[] = [];
  isHovered!: boolean;
  isToggle!: boolean;

  @ViewChild('screenContainer', { static: true }) screenContainer!: ElementRef;
  @ViewChild('imageContainer', { static: true }) imageContainer!: ElementRef;
  @ViewChild('readingContainer', { static: true })
  readingContainer!: ElementRef;

  @ViewChild('ToggleMenuButton') ToggleMenuButton!: ElementRef;

  @ViewChild('controlBar') controlBar!: ElementRef;
  @ViewChild('MenuNavigation')
  MenuNavigation!: ElementRef;
  @ViewChild('commentComponent')
  commentComponent!: ElementRef;
  @ViewChild('HeaderContainer')
  HeaderContainer!: ElementRef;
  @ViewChild('EndChapter')
  endChapterElement!: ElementRef;

  @ViewChild(FadeInDirective) fadeInDirective?: FadeInDirective;

  private readonly zoomStep: number = 0.1;
  private readonly minZoomLevel: number = -0.5;
  private readonly maxZoomLevel: number = 0.5;
  public stickyState: StickyState = StickyState.NoneSticky;
  public zoomLevel = 0;
  public defaultZoomLevel = 0;
  public defaultWidth = 0;
  public isLimitZoom = false;
  private lastScrollTop = 0;
  private istotaly = false;

  elementOffset = 0;

  isVertical = true;
  showScrollToTop = false;
  isAutoNextChapter = false;
  isNightMode = false;
  isFullScreen = false;
  isLoading = true;
  isFooterBannerVisible = true;
  isLeftBannerVisible = true;
  isRightBannerVisible = true;
  selectedServerId: number = 0;
  showAllServers = false;

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private historyService: HistoryService,
    private seoService: SeoService,
    private renderer: Renderer2,
    private storage: StorageService,
    protected override cd: ChangeDetectorRef,
    private popupService: PopupService,
    private urlService: UrlService,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
    // this.ListChapterImg = new Array<Page>(10).fill({} as Page);
  }

  closeLeftBanner(): void {
    this.isLeftBannerVisible = false;
  }

  closeRightBanner(): void {
    this.isRightBannerVisible = false;
  }

  closeFooterBanner(): void {
    this.isFooterBannerVisible = false;
  }

  ngAfterViewInit(): void {
    // this.readingContainer.nativeElement.clientWidth = '100px';
    this.runInBrowser(() => {
      this.defaultWidth = this.HeaderContainer.nativeElement.clientWidth;
      this.defaultZoomLevel = this.storage.GetZoomImage();

      this.zoomLevel = this.defaultZoomLevel;
      this.isToggle = false;
      this.ToggleMenu(false);
      this.safeMarkForCheck();
    });
  }
  ngAfterViewChecked(): void {
    if (this.controlBar && this.elementOffset === 0) {
      this.elementOffset = this.controlBar.nativeElement.offsetTop;
    }
  }
  override ngOnDestroy(): void {

    ChatBoxBubbleComponent.Instance?.SetVisible(true);
  }
  ngOnInit(): void {
    this.runInBrowser(() => {
      ChatBoxBubbleComponent.Instance?.SetVisible(false);
    })
    this.isLoading = true;
    this.route.data.subscribe(({ ChapterImgRes }) => {
      if (ChapterImgRes === null || ChapterImgRes.data === null) {
        this.router.navigate(['/']);
        return;
      }
      const data = structuredClone(ChapterImgRes.data);
      this.listChapterServerIds = [0, ...data.chapterServerIds];
      this.isLoading = false;
      this.comic = data.comic;
      this.mainChapter = data;
      this.listImg = this.mainChapter.pages;
      this.selectedServerId = 0;
      this.runInBrowser(() => {
        this.historyService.SaveHistory(this.comic, {
          id: this.mainChapter.id,
          title: this.mainChapter.title,
          slug: this.mainChapter.slug,
          updateAt: this.mainChapter.updateAt,
          viewCount: this.mainChapter.viewCount,
        });
        this.comicService
          .getChapters(this.comic.id)
          .subscribe((res: IServiceResponse<Chapter[]>) => {
            this.listChapters = res.data || [];
            this.safeMarkForCheck();
          });
      })

      this.SetupSeo();
      this.istotaly = false;
      this.safeMarkForCheck();
    });
  }
  changeServer(serverId: number, index: number) {
    this.selectedServerId = index;
    if (this.selectedServerId == 0) {
      this.listImg = this.mainChapter.pages;
      this.safeMarkForCheck();

      return
    }

    if (!serverId) {
      return;
    }

    this.comicService
      .getChapterServer(serverId)
      .subscribe((res: IServiceResponse<ChapterServer>) => {
        const pagesImages = res.data?.images ?? [];
        this.listImg = pagesImages.map((img) => {
          return {
            url: img,
            pageNumber: 0,
          };
        });
        this.safeMarkForCheck();

      });
  }
  showMoreServer() {
    this.showAllServers = !this.showAllServers;
  }
  ToggleMenu(stage: boolean) {
    this.isToggle = stage;
    if (!this.MenuNavigation.nativeElement) return;
    const _class =
      this.stickyState === StickyState.StickyBottom
        ? 'translate-bottom'
        : 'translate-top';
    if (!stage) {
      this.MenuNavigation.nativeElement.classList.add('hidden');
      
    } else {
      this.renderer.addClass(this.MenuNavigation.nativeElement, _class);
      this.MenuNavigation.nativeElement.classList.remove('hidden');
    }
  }
  onChangeChapter(event: any) {
    this.OnChangeChapter(event.target.value);
  }

  OnChangeChapter(chapterId: number) {
    this.isLoading = true;
    this.router.navigate(['truyen-tranh', this.comic.url, chapterId]);
  }

  @HostListener('document:click', ['$event'])
  onClickOutside(event: MouseEvent): void {
    if (!this.isToggle) return;
    if (
      !this.MenuNavigation.nativeElement.contains(event.target) &&
      !this.ToggleMenuButton.nativeElement.contains(event.target)
    ) {
      this.ToggleMenu(false);
    }
  }

  ZoomImage(zoomIn: boolean): void {
    let currentZoomLevel = this.zoomLevel;
    if (zoomIn) {
      this.zoomLevel = Math.min(
        this.zoomLevel + this.zoomStep,
        this.maxZoomLevel
      );
    } else {
      this.zoomLevel = Math.max(
        this.zoomLevel - this.zoomStep,
        this.minZoomLevel
      );
    }
    if (this.zoomLevel == this.minZoomLevel) {
      this.isLimitZoom = false;
      return;
    }
    if (this.zoomLevel == this.maxZoomLevel) {
      this.isLimitZoom = true;
      return;
    }

    currentZoomLevel -= this.zoomLevel;
    this.isLimitZoom = currentZoomLevel > 0;

    this.storage.SetZoomImage(this.zoomLevel);
  }

  resetView(): void {
    this.isLimitZoom = false;
    this.zoomLevel = this.defaultZoomLevel;
  }

  getZoomPercentage(): number {
    return Math.round((this.zoomLevel - this.defaultZoomLevel) * 100) + 100;
  }

  @HostListener('document:keydown.arrowleft', ['$event'])
  onNextChapter(): void {
    this.navigateChapter(true);
  }
  @HostListener('document:keydown.arrowright', ['$event'])
  onPreviousChapter(): void {
    this.navigateChapter(false);
  }

  navigateChapter = throttle((isNext: boolean): void => {
    if (this.isLoading) {
      return;
    }

    const container = this.imageContainer.nativeElement;
    const currentChapterIndex = this.listChapters!.findIndex(
      (chapter) => chapter.id === this.mainChapter.id
    );
    const targetChapterIndex = isNext
      ? currentChapterIndex + 1
      : currentChapterIndex - 1;
    // console.log(targetChapterIndex, this.listChapters!.length);

    if (
      targetChapterIndex >= 0 &&
      targetChapterIndex < this.listChapters!.length
    ) {
      const targetChapter = this.listChapters![targetChapterIndex];
      this.OnChangeChapter(targetChapter.id);
      container.scrollLeft = 0;
      container.scrollTop = 0;
    }
  }, 1000);

  enterFullscreen(): void {
    const elem = this.imageContainer.nativeElement;
    this.isFullScreen = true;
    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    } else if (elem.mozRequestFullScreen) {
      // Firefox
      elem.mozRequestFullScreen();
    } else if (elem.webkitRequestFullscreen) {
      // Chrome, Safari, and Opera
      elem.webkitRequestFullscreen();
    } else if (elem.msRequestFullscreen) {
      // IE/Edge
      elem.msRequestFullscreen();
    }
  }

  scrollToTop(event: Event): void {
    event.preventDefault();
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  toggleHover(state: boolean) {
    this.isHovered = state;
  }

  onCheckboxChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.isAutoNextChapter = checkbox.checked;
  }

  @HostListener('window:resize', ['$event'])
  onResize(_event: Event) {
    this.defaultWidth = this.HeaderContainer.nativeElement.clientWidth;
    this.cd.markForCheck();
  }
  @HostListener('window:scroll', ['$event'])
  handleScroll() {
    if (!this.controlBar) return;
    const windowScroll = window.scrollY;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;

    const isLastPage = windowScroll + windowHeight >= documentHeight - 1;
    if (isLastPage) {
      if (this.isAutoNextChapter && this.isVertical) {
        this.navigateChapter(false);
        return;
      } else {
        this.showScrollToTop = true;
        return;
      }
    }
    // this.isStickyBottom = (windowScroll + windowHeight > this.endChapterElement.nativeElement.offsetTop)
    this.updateStickerState();

    this.lastScrollTop = windowScroll <= 0 ? 0 : windowScroll;

    const scrollOffset =
      window.scrollY ||
      document.documentElement.scrollTop ||
      document.body.scrollTop ||
      0;

    if (!this.istotaly && scrollOffset > 2000) {
      this.istotaly = true;

      this.comicService
        .updateViewAndExp(
          this.comic.id,
          this.mainChapter.id,
          UserExpType.Chapter
        )
        .subscribe(() => { });
    }
  }
  startTickTop = 0;

  // Performance optimization methods
  trackByPageId = (index: number, page: Page): any => {
    return page?.url ?? index;
  };

  updateStickerState() {
    const windowScroll = window.scrollY;
    const windowHeight = window.innerHeight;
    const preStickyPos = this.stickyState;
    const isEndChapter =
      windowScroll + windowHeight >
      this.endChapterElement.nativeElement.offsetTop;
    if (isEndChapter) {
      this.stickyState = StickyState.StickyBottom;
    }

    if (!isEndChapter) {
      if (windowScroll > this.elementOffset) {
        if (windowScroll < this.lastScrollTop) {
          this.stickyState = StickyState.StickyTop;
          this.showScrollToTop = true;
          this.startTickTop = windowScroll;
        } else {
          if (
            this.stickyState == StickyState.StickyTop &&
            windowScroll - this.startTickTop > 200
          ) {
            this.stickyState = StickyState.StickyInvisible;
            this.showScrollToTop = false;
          }
        }
      } else {
        this.stickyState = StickyState.NoneSticky;
      }
    }
    if (preStickyPos != this.stickyState) {
      this.ToggleMenu(false);
      switch (this.stickyState as StickyState) {
        case StickyState.StickyBottom:
          this.stickToBottom();
          break;
        case StickyState.StickyTop:
          this.stickToTop();
          break;
        case StickyState.StickyInvisible:
          this.showSticky(false);
          break;
        case StickyState.NoneSticky:
          this.cancelSticky();
          break;
      }
    }
  }
  cancelSticky() {
    this.showSticky(true);
    this.controlBar.nativeElement.classList.remove(
      'sticky-bottom',
      'sticky-top'
    );
    this.renderer.addClass(this.controlBar.nativeElement, 'rounded-b-lg');
  }
  stickToTop() {
    this.showSticky(true);

    this.renderer.addClass(this.controlBar.nativeElement, 'sticky-top');
    this.renderer.removeClass(this.controlBar.nativeElement, 'rounded-b-lg');
    this.controlBar.nativeElement.classList.remove('sticky-bottom');
  }
  stickToBottom() {
    this.showSticky(true);
    this.renderer.addClass(this.controlBar.nativeElement, 'sticky-bottom');
    this.controlBar.nativeElement.classList.remove('sticky-top');
  }

  showSticky(isVisible: boolean) {
    if (isVisible) {
      this.fadeInDirective?.fadeIn();
    } else {
      this.fadeInDirective?.fadeOut();
    }
  }

  changeDirectionReading(stage: boolean) {
    this.isVertical = stage;
    this.addMouseWheelEvent();
    const styles = this.isVertical
      ? {
        'scroll-snap-align': 'start',
        flex: '0 0 auto',
        display: 'flex',
        'flex-direction': 'column',
        'overflow-y': 'auto',
        'overflow-x': 'hidden',
      }
      : {
        'margin-top': '30px',
        'min-width': '30rem',
        'scroll-snap-align': 'start',
        display: 'flex',
        'flex-direction': 'row',
        overflow: 'hidden',
        'overflow-x': 'auto',
        'overflow-y': 'hidden',
      };

    for (const [key, value] of Object.entries(styles)) {
      this.renderer.setStyle(this.imageContainer.nativeElement, key, value);
    }
  }

  enableNightLight(stage: boolean) {
    this.isNightMode = stage;
  }

  addMouseWheelEvent() {
    const container = this.imageContainer.nativeElement;
    container.removeEventListener('wheel', this.handleWheelEvent);
    container.addEventListener('wheel', this.handleWheelEvent);
  }

  scrollHorizontal(direction: number) {
    const container = this.imageContainer.nativeElement;
    const scrollAmount = direction * 500;
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth',
    });
    if (this.isAutoNextChapter) {
      this.checkScrollEnd();
    }
  }

  handleWheelEvent = (event: WheelEvent) => {
    const container = this.imageContainer.nativeElement;
    const scrollGap = 2.5;
    if (!this.isVertical) {
      container.scrollBy({
        left: event.deltaY * scrollGap,
        behavior: 'smooth',
      });
      if (this.isAutoNextChapter) {
        this.checkScrollEnd();
      }
      event.preventDefault();
    }
  };

  checkScrollEnd() {
    const container = this.imageContainer.nativeElement;

    if (this.isVertical) {
      if (
        container.scrollTop + container.clientHeight >=
        container.scrollHeight
      ) {
        this.navigateChapter(true);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    } else {
      if (
        container.scrollLeft + container.clientWidth >=
        container.scrollWidth
      ) {
        this.navigateChapter(false);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    }
  }
  SetupSeo() {
    const title = `${this.comic.title} Chapter ${this.mainChapter.slug} - MeTruyenMoi`;
    const description = `[Chapter ${this.mainChapter.slug
      }] - ${ServiceUtility.fillSeoDescription(this.comic.description, {
        title: this.comic.title,
      })} - MeTruyenMoi`;
    const url = `${this.urlService.BASE_URL}/truyen-tranh/${this.comic.url}/${this.mainChapter.id}`;
    this.seoService.setTitle(title);
    this.seoService.addTags([
      { name: 'description', content: description },
      {
        name: 'keywords',
        content: ` ${this.comic.title}, ${this.comic.url}, ${this.comic.title} chapter ${this.mainChapter.slug} ,Mê Truyện Mới, metruyenmoi`,
      },
      { property: 'og:description', content: description },
      { property: 'og:title', content: title },
      { property: 'og:url', content: url },
      { property: 'og:type', content: 'article' },
      { property: 'og:site_name', content: 'MeTruyenMoi' },
      { itemprop: 'name', content: this.comic.title },
      { itemprop: 'description', content: description },
    ]);
    this.seoService.updateLink('canonical', url);
  }

  reportError() {
    this.popupService.showReportComic({
      comicID: this.comic.id,
      chapterID: this.mainChapter.id,
    });
  }

  get TopToBottom() {
    return this.stickyState !== StickyState.StickyBottom;
  }
}
