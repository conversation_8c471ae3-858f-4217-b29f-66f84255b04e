using ComicApp.Models;

namespace ComicAPI.DTOs
{
    public class MessageDTO
    {
        public Guid ID { get; set; }
        public int UserId { get; set; }
        public string Content { get; set; } = string.Empty;
        public Guid RoomId { get; set; }
        public DateTime CreatedAt { get; set; }
        public UserDTO? User { get; set; }

        public MessageDTO() { }

        public MessageDTO(Message message)
        {
            ID = message.ID;
            UserId = message.UserId;
            Content = message.Content;
            RoomId = message.RoomId;
            CreatedAt = message.CreatedAt;

            if (message.User != null)
                User = new UserDTO(message.User);
        }
    }

    public class SendMessageDTO
    {
        public string Content { get; set; } = string.Empty;
        public Guid? ConversationId { get; set; }
        public int? TargetUserId { get; set; } // For creating new conversation
        public bool IsWorldChat { get; set; } = false;
    }

    public class MessagePageDTO
    {
        public List<MessageDTO> Messages { get; set; } = new List<MessageDTO>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public bool HasNextPage { get; set; }
    }
}
