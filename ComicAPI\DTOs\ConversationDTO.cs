using ComicApp.Models;

namespace ComicAPI.DTOs
{
    public class ConversationDTO
    {
        public Guid ID { get; set; }
        public string? Name { get; set; }
        public int UserIdA { get; set; }
        public int AgentId { get; set; }
        public int UserIdB { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Guid? LastMessageId { get; set; }
        public UserDTO? UserA { get; set; }
        public UserDTO? UserB { get; set; }
        public UserDTO? Agent { get; set; }
        public List<MessageDTO> Messages { get; set; } = new List<MessageDTO>();
        public MessageDTO? LastMessage { get; set; }

        public ConversationDTO() { }

        public ConversationDTO(Conversation conversation)
        {
            ID = conversation.ID;
            Name = conversation.Name;
            UserIdA = conversation.UserIdA;
            AgentId = conversation.AgentId;
            UserIdB = conversation.UserIdB;
            CreatedAt = conversation.CreatedAt;
            UpdatedAt = conversation.UpdatedAt;
            LastMessageId = conversation.LastMessageId;

            if (conversation.UserA != null)
                UserA = new UserDTO(conversation.UserA);
            
            if (conversation.UserB != null)
                UserB = new UserDTO(conversation.UserB);
            
            if (conversation.Agent != null)
                Agent = new UserDTO(conversation.Agent);

            Messages = conversation.Messages?.Select(m => new MessageDTO(m)).ToList() ?? new List<MessageDTO>();
        }
    }

    public class CreateConversationDTO
    {
        public string? Name { get; set; }
        public int AgentId { get; set; }
        public int UserIdB { get; set; }
    }

    public class ConversationListDTO
    {
        public Guid ID { get; set; }
        public string? Name { get; set; }
        public UserDTO? OtherUser { get; set; }
        public UserDTO? Agent { get; set; }
        public MessageDTO? LastMessage { get; set; }
        public DateTime UpdatedAt { get; set; }
        public bool IsWorldChat { get; set; }
    }
}
