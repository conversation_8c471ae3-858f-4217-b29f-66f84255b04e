import { CommonModule, isPlatformServer } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  PLATFORM_ID,
  signal,
  SimpleChanges,
  TrackByFunction,
  AfterViewInit,
} from '@angular/core';
import { Comic } from '@schema';
import { StorageService } from '@services/storage.service';
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { ComicCardComponent } from '../comic-card/Ver1/comic-card.component';
import { ComicCardV2Component } from '../comic-card/Ver2/comic-card-v2.component';
import { PopupDetailComicComponent } from '../../lazy/popup-detail-comic/popup-detail-comic.component';
@Component({
  selector: 'app-grid-comic',
  templateUrl: './grid-comic.component.html',
  styleUrl: './grid-comic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule, ComicCardComponent, ComicCardV2Component, PopupDetailComicComponent],
})
export class GridComicComponent extends OptimizedBaseComponent implements  OnDestroy {
  @Input() nPreview = 40;
  @Input() _class!: string;
  @Input() title!: string;
  @Output() clickEvent = new EventEmitter<number[]>();
  @Input() eventName!: string;

  @Input()
  set listComics(value: Comic[]) {
    if(value.length == 0) 
    {
      this.listComicsSignal.set(Array(this.nPreview).fill(undefined));
      return;
    }
    if (value !== this.listComicsSignal()) {
      this.listComicsSignal.set(value || Array(this.nPreview).fill(undefined));
    }
  }

  // Signals for reactive state management
  private readonly girdTypeSignal = signal(0);
  private readonly listComicsSignal = signal<Comic[]>([]);
  private readonly hoverComicSignal = signal<Comic | undefined>(undefined);
  private readonly selectedComicsSignal = signal(new Set<number>());
  private readonly isSelectAllSignal = signal(false);

  // Computed properties for optimized access
  readonly girdType = computed(() => this.girdTypeSignal());
  readonly _listComics = computed(() => this.listComicsSignal());
  readonly hoverComic = computed(() => this.hoverComicSignal());
  readonly selectedComics = computed(() => this.selectedComicsSignal());
  readonly isSelectAll = computed(() => this.isSelectAllSignal());
  readonly selectedCount = computed(() => this.selectedComics().size);
  readonly hasSelectedComics = computed(() => this.selectedCount() > 0);

  // TrackBy functions for performance
  readonly trackByComicId: TrackByFunction<Comic> = (index: number, comic: Comic) => {
    return comic?.id ?? index;
  };

  override readonly trackByIndex = (index: number) => index;

  // Performance optimizations

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
    private storageService: StorageService,
  ) {
    super(cd, platformId);
    this.listComics = Array(this.nPreview).fill(undefined);
    this.girdTypeSignal.set(this.storageService.GetGridType());

    // Debounced resize handler

  }


  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }



  onHoverComic(comic?: Comic) {
    this.hoverComicSignal.set(comic);
  }

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    if (this.hoverComic()) {
      this.hoverComicSignal.set(undefined);
    }
  }

  changeGridType(_: any, type: number) {
    this.girdTypeSignal.set(type);
    this.storageService.SetGridType(type);
  }

  toggleSelectComic(index: number) {
    const currentSelected = new Set(this.selectedComics());
    if (currentSelected.has(index)) {
      currentSelected.delete(index);
    } else {
      currentSelected.add(index);
    }
    this.selectedComicsSignal.set(currentSelected);
  }

  selectAllComics(stage: boolean) {
    this.isSelectAllSignal.set(stage);
    const newSelected = new Set<number>();
    if (stage) {
      this._listComics().forEach((_, index) => newSelected.add(index));
    }
    this.selectedComicsSignal.set(newSelected);
  }

  deleteSelectedComics() {
    const selectedSet = this.selectedComics();
    if (!selectedSet.size) {
      return;
    }
    const selectedComicIds = Array.from(selectedSet).map(
      (index) => this._listComics()[index].id,
    );
    this.clickEvent.emit(selectedComicIds);
  }

}
