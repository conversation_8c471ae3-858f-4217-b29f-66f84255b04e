import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { EmojiComponent } from '@components/lazy/emoji/emoji.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IConversation, IConversationList, IMessage, IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ChatService } from '@services/chat.service';
import { ChatbotService } from '@services/chatbot.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';
@Component({
  imports: [CommonModule, EmojiComponent, ClickOutsideDirective,],
  selector: 'app-chat-box',
  templateUrl: './chat-box.component.html',
  styleUrl: './chat-box.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ChatBoxComponent implements OnInit, AfterViewInit, IPopupComponent {
  hideChatChange = new EventEmitter<boolean>();
  isShowChatBox = true;
  isShowChatMenu = false;
  activeEmojiPicker = false;
  @ViewChild('chatBox') chatBoxRef?: ElementRef;
  @ViewChild('chatMenu') chatMenuRef?: ElementRef;
  loginUser: IUser | undefined = undefined
  conversations: IConversationList[] = [];
  currentConversation: IConversation | null = null;
  messages: IMessage[] = [];
  contactList = [
    {
      userId: 2,
      name: 'AI Chat bot',
      path: '/option1.png',
      time: '12:09 PM',
      preview: 'Tính năng đã ra mắt',
      messages: [
        {
          fromUserId: 0,
          toUserId: 2,
          text: 'Xin chào bạn. Moi có thể giúp gì cho bạn?',
          time: '',
        },

      ],
      active: false,
    },
  ];
  searchUser = '';
  textMessage: string | null = '';
  selectedUser: any = null;
  constructor(
    private cdref: ChangeDetectorRef,
    private chabotService: ChatbotService,
    private chatService: ChatService,
    private accountService: AccountService,
    private toastService: ToastService,
  ) {

  }
  show(object: any): Promise<any> {
    this.isShowChatBox = true;
    this.cdref.detectChanges();
    return new Promise((resolve) => {
      resolve({});
    });
  }
  ngOnInit(): void {
    this.loginUser = this.accountService.GetUser()
    this.loadConversations();
    this.selectUser(this.contactList[0]);
  }
  ngAfterViewInit() {
    this.scrollToBottom();
  }
  searchUsers() {
    return this.contactList.filter((d: { name: string }) => {
      return d.name.toLowerCase().includes(this.searchUser);
    });
  }

  selectUser(user: any) {
    this.selectedUser = user;
    this.currentConversation = null; // Reset current conversation when selecting mock user
    this.messages = []; // Clear real messages
    this.isShowChatMenu = false;
    this.scrollToBottom();
  }

  toggleEmojiPicker() {
    this.activeEmojiPicker = !this.activeEmojiPicker;
  }

  sendMessage() {
    // Check if we have a current conversation, use real chat
    if (this.currentConversation) {
      this.sendRealMessage();
      return;
    }
    console.log(this.currentConversation);
    

    // Fallback to old chatbot logic for AI Chat bot
    const messager: any = this.contactList.find(
      (d: { userId: any }) => d.userId === this.selectedUser.userId
    );

    if (this.selectedUser.userId === 2) { // AI Chat bot
      messager.messages.push({
        fromUserId: this.selectedUser.userId,
        toUserId: 0,
        text: this.textMessage,
        time: 'Just now',
      });

      if (this.textMessage) {
        const eventSource = this.chabotService.Chat(
          this.textMessage,
          false,
          5
        );
        if (!eventSource) {
          this.toastService.show(ToastType.Error, 'Vui lòng đăng nhập để sử dụng ChatBot!')
          return
        }
        let isFirstChunk = true;
        eventSource.onmessage = (event: any) => {
          console.log(event);
          
          let text = event.data
try {

          const parsedData = JSON.parse(event.data);
          if (parsedData.text === '[DONE]') {
            eventSource.close();
          } else if (parsedData.text) {
            const partialText =
              this.processTemplate(parsedData.text);

            if (isFirstChunk) {
              messager.messages.push({
                fromUserId: 0,
                toUserId: 2,
                text: partialText,
                time: 'Just now',
              });
              isFirstChunk = false;
            } else {
              messager.messages[messager.messages.length - 1].text += partialText;
            }

            this.cdref.detectChanges(); // Cập nhật giao diện
            this.scrollToBottom(); // Cuộn xuống cuối
          }
        } catch (e) {
          console.error('Failed to parse event data:', e);
        }
        };
        eventSource.onerror = (error: any) => {
          console.error('EventSource error:', error);
          eventSource.close();
        };
        this.textMessage = '';
        this.cdref.detectChanges();
        this.scrollToBottom();
      
      }
    } else {
      // For other mock conversations, use real chat
      this.sendRealMessage();
    }
  }
  processTemplate(text: string) {
    // Loại bỏ hoặc thay thế ký tự template
// 1. Chuyển đổi tiêu đề (H1, H2)
    // Cần xử lý từ lớn đến nhỏ để tránh trùng khớp sai
    text = text.replace(/^##\s*(.*)$/gm, '<h2>$1</h2>'); // H2
    text = text.replace(/^#\s*(.*)$/gm, '<h1>$1</h1>');  // H1

    // 2. In đậm: **text** hoặc __text__
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/__(.*?)__/g, '<strong>$1</strong>');

    // 3. In nghiêng: *text* hoặc _text_
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
    text = text.replace(/_(.*?)_/g, '<em>$1</em>');

    // 5. Xuống dòng: Chuyển \n thành <br> (trừ khi nó nằm trong thẻ text đã được tạo)
    // Đây là phần cần cân nhắc cẩn thận, vì có thể phá vỡ text
    // Một cách đơn giản là chuyển tất cả \n thành <br> nhưng có thể không tối ưu
    // Đối với ví dụ này, chúng ta sẽ chuyển các dòng mới không nằm trong block text.
    // Cách an toàn hơn là xử lý đoạn văn và sau đó thêm <br>
    // Để đơn giản, ví dụ này sẽ chỉ thay thế \n bằng <br>
    text = text.replace(/\n/g, '<br>');
    return text;
  }
  closeChat() {
    this.hideChatChange.emit(true);
  }
  setVisible(isVisible: boolean) {
    this.isShowChatBox = isVisible;
  }

  msgChange(event: any) {
    this.textMessage = event.target.value;
  }

  addEmoji(event: any): void {
    return;
  }

  scrollToBottom() {
    // const chatBox = document.getElementById('chatBox');
    const chatBox = this.chatBoxRef?.nativeElement;
    chatBox?.scrollTo({
      top: chatBox.scrollHeight,
      left: 0,
      behavior: 'smooth',
    });
  }

  // New methods for real chat functionality
  loadConversations() {
    if (!this.loginUser) return;

    this.chatService.getMyConversations().subscribe({
      next: (response) => {
        if (response.status === 1 && response.data) {
          this.conversations = response.data;
          this.cdref.detectChanges();
        }
      },
      error: (error) => {
        console.error('Error loading conversations:', error);
      }
    });

    // Also load world chat
    this.chatService.getWorldChat().subscribe({
      next: (response) => {
        if (response.status === 1 && response.data) {
          // Add world chat to conversations if not exists
          const worldChatExists = this.conversations.some(c => c.isWorldChat);
          if (!worldChatExists) {
            this.conversations.unshift({
              id: response.data.id,
              name: 'Chat thế giới',
              users: [],
              lastMessage: response.data.messages?.[response.data.messages.length - 1],
              updatedAt: response.data.updatedAt,
              isWorldChat: true
            });
            this.cdref.detectChanges();
          }
        }
      },
      error: (error) => {
        console.error('Error loading world chat:', error);
      }
    });
  }

  selectConversation(conversation: IConversationList) {
    this.chatService.getConversation(conversation.id).subscribe({
      next: (response) => {
        if (response.status === 1 && response.data) {
          this.currentConversation = response.data;
          this.messages = response.data.messages || [];
          this.isShowChatMenu = false;
          this.cdref.detectChanges();
          this.scrollToBottom();
        }
      },
      error: (error) => {
        console.error('Error loading conversation:', error);
        this.toastService.show(ToastType.Error, 'Không thể tải cuộc trò chuyện');
      }
    });
  }

  sendRealMessage() {
    if (!this.textMessage || !this.loginUser) return;

    const content = this.textMessage.trim();
    if (!content) return;

    // Check if this is world chat
    if (this.currentConversation && this.isWorldChat()) {
      this.chatService.sendWorldChatMessage(content).subscribe({
        next: (response) => {
          if (response.status === 1 && response.data) {
            this.messages.push(response.data);
            this.textMessage = '';
            this.cdref.detectChanges();
            this.scrollToBottom();
          }
        },
        error: (error) => {
          console.error('Error sending world chat message:', error);
          this.toastService.show(ToastType.Error, 'Không thể gửi tin nhắn');
        }
      });
    } else if (this.currentConversation) {
      // Send regular message
      const request = {
        content: content,
        conversationId: this.currentConversation.id,
        isWorldChat: false
      };

      this.chatService.sendMessage(request).subscribe({
        next: (response) => {
          if (response.status === 1 && response.data) {
            this.messages.push(response.data);
            this.textMessage = '';
            this.cdref.detectChanges();
            this.scrollToBottom();
          }
        },
        error: (error) => {
          console.error('Error sending message:', error);
          this.toastService.show(ToastType.Error, 'Không thể gửi tin nhắn');
        }
      });
    }
  }

  isWorldChat(): boolean {
    return this.currentConversation?.name === 'Chat thế giới';
  }

  createNewConversation(targetUserId: number) {
    this.chatService.createOrGetConversation(targetUserId).subscribe({
      next: (response) => {
        if (response.status === 1 && response.data) {
          this.currentConversation = response.data;
          this.messages = response.data.messages || [];
          this.loadConversations(); // Refresh conversation list
          this.cdref.detectChanges();
          this.scrollToBottom();
        }
      },
      error: (error) => {
        console.error('Error creating conversation:', error);
        this.toastService.show(ToastType.Error, 'Không thể tạo cuộc trò chuyện');
      }
    });
  }
}
