import { HttpClient } from "@angular/common/http"; // Import HttpHeaders
import { Injectable, NgZone } from "@angular/core";
import { environment } from "../environment";

@Injectable({ providedIn: 'root' })
export class VersionCheckService {
    private lastKnownVersion: string = environment.version;

    constructor(private http: HttpClient, private ngZone: NgZone) { }

    checkVersionPeriodically(intervalMs: number = 90_000): void {
        this.ngZone.runOutsideAngular(() => {
            setInterval(() => {
                this.checkVersion();
            }, intervalMs);
        });
    }

    private checkVersion(): void {
        // Create HttpHeaders to prevent cachin

        this.http.get<{ version: string }>('/version.json').subscribe({
            next: (res) => {
                const newVersion = res.version;
                if (this.lastKnownVersion !== newVersion) {
                    alert('Me tryện moi đã có phiên bản mới, vui lòng tải lại trang.');
                    window.location.reload();
                }
            },
            error: (err) => {
                console.error('Error checking app version:', err);
            }
        });
    }
}