import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, Output, ChangeDetectorRef, Inject, PLATFORM_ID, signal, computed } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { NumeralPipe } from '@pines/numeral.pipe';
import { Comic } from '@schema';
import { OptimizedBaseComponent } from '../../base/optimized-base.component';

@Component({
    selector: 'app-comic-card',
    templateUrl: './comic-card.component.html',
    styleUrl: './comic-card.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule, RouterLink, NumeralPipe, DateAgoPipe],
})
export class ComicCardComponent extends OptimizedBaseComponent implements OnD<PERSON>roy {
  @Input()
  set comic(value: Comic | undefined) {
    if (value !== this.comicSignal()) {
      this.comicSignal.set(value);
    }
  }
  get comic(): Comic | undefined {
    return this.comicSignal();
  }

  @Input() eventName?: string;
  @Output() comicHover = new EventEmitter<Comic | undefined>();
  @Output() clickEvent = new EventEmitter<number>();

  // Signals for reactive state
  private readonly comicSignal = signal<Comic | undefined>(undefined);

  // Computed properties for optimized access
  readonly comicData = computed(() => this.comicSignal());
  readonly comicRouterLink = computed(() => {
    const comic = this.comicData();
    return comic ? ['/truyen-tranh', `${comic.url}-${comic.id}`] : [];
  });
  readonly chapterRouterLink = computed(() => {
    const comic = this.comicData()
    if (!comic?.chapters?.[0]) return [];
    return ['/truyen-tranh', comic.url, comic.chapters[0].id.toString()];
  });
  readonly hasChapters = computed(() => !!(this.comicData()?.chapters?.length));
  readonly authorName = computed(() => this.comicData()?.author ?? 'Đang cập nhật');

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  onHoverComic = (hover: boolean): void => {
    this.comicHover.emit(hover && this.comic ? this.comic : undefined);
  };

  onClick = (): void => {
    if (this.comic?.id) {
      this.clickEvent.emit(this.comic.id);
    }
  };

  override ngOnDestroy(): void {
    this.comicHover.emit(undefined);
    super.ngOnDestroy();
  }
}
